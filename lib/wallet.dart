import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:user/fetchuserapi.dart';
import 'dart:convert';

import 'package:user/usermodel.dart';

class UserListScreen extends StatefulWidget {
  @override
  _UserListScreenState createState() => _UserListScreenState();
}

class _UserListScreenState extends State<UserListScreen> {
  late Future<List<User>> futureUsers;
  bool _isLoading = false;
  bool _isCommonWalletLoading = false;

  late List<User> allUsers = [];
  List<User> filteredUsers = [];
  TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    futureUsers = fetchUsers();
    futureUsers.then((users) {
      setState(() {
        allUsers = users;
        filteredUsers = users; // Initially, display all users
      });
    });
  }

  Future<void> _refreshUsers() async {
    futureUsers = fetchUsers();
    futureUsers.then((users) {
      setState(() {
        allUsers = users;
        filteredUsers = users; // Reset filtered users
      });
    });
  }

  void _filterUsers(String query) {
    final results = allUsers.where((user) {
      final userName = user.name.toLowerCase();
      final input = query.toLowerCase();
      return userName.contains(input);
    }).toList();

    setState(() {
      filteredUsers = results;
    });

    print('Filtered Users: $filteredUsers');
  }

  void _showUpdateDialog(User user) {
    final TextEditingController _amountController = TextEditingController();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Update wallet amount'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _amountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: 'Enter amount',
                ),
              ),
              SizedBox(height: 20),
              if (_isLoading) // Show loading indicator if _isLoading is true
                Center(
                    child: CircularProgressIndicator(
                  color: Colors.red,
                  strokeWidth: 2,
                )),
              if (!_isLoading) // Show buttons if not loading
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue),
                      onPressed: () {
                        _updateAmount(
                            user.id.toString(), _amountController.text, "1");
                      },
                      child: Text('Add', style: TextStyle(color: Colors.white)),
                    ),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue),
                      onPressed: () {
                        _updateAmount(
                            user.id.toString(), _amountController.text, "0");
                      },
                      child:
                          Text('Reduce', style: TextStyle(color: Colors.white)),
                    ),
                  ],
                ),
            ],
          ),
        );
      },
    );
  }

  void _showCommonWalletUpdateDialog() {
    final TextEditingController _amountController = TextEditingController();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Update common wallet amount'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _amountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: 'Enter amount',
                ),
              ),
              SizedBox(height: 20),
              if (_isCommonWalletLoading) // Show loading indicator if _isCommonWalletLoading is true
                Center(child: CircularProgressIndicator()),
              if (!_isCommonWalletLoading) // Show buttons if not loading
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue),
                      onPressed: () {
                        _updateCommonWallet(_amountController.text, "1");
                      },
                      child: Text('Add', style: TextStyle(color: Colors.white)),
                    ),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue),
                      onPressed: () {
                        _updateCommonWallet(_amountController.text, "0");
                      },
                      child:
                          Text('Reduce', style: TextStyle(color: Colors.white)),
                    ),
                  ],
                ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _updateAmount(String userId, String amount, String type) async {
    setState(() {
      _isLoading = true; // Set loading state to true
    });

    final response = await http.post(
      Uri.parse('https://price.zoomfresh.co.in/api/updatewallet'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_API_TOKEN_HERE',
      },
      body: jsonEncode({
        'user_id': userId, // Use the user ID of the clicked user
        'amount': amount,
        'type': type,
      }),
    );

    setState(() {
      _isLoading = false; // Set loading state to false
    });

    if (response.statusCode == 200) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Amount updated successfully'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.of(context).pop(); // Close the dialog
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update amount'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _updateCommonWallet(String amount, String type) async {
    setState(() {
      _isCommonWalletLoading = true; // Set loading state to true
    });

    final response = await http.post(
      Uri.parse('https://price.zoomfresh.co.in/api/updatewalletcommon'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_API_TOKEN_HERE',
      },
      body: jsonEncode({
        'amount': amount,
        'type': type,
      }),
    );

    setState(() {
      _isCommonWalletLoading = false; // Set loading state to false
    });

    if (response.statusCode == 200) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Common wallet updated successfully'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.of(context).pop(); // Close the dialog
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update common wallet'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        title: Text(
          'Wallet',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.red,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize:
              Size.fromHeight(70), // Adjust the height for the search box
          child: Column(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(
                        25), // Circular edges for the search box
                  ),
                  child: TextField(
                    controller: searchController,
                    onChanged: _filterUsers,
                    decoration: InputDecoration(
                      hintText: 'Search users by name',
                      hintStyle: TextStyle(color: Colors.grey),
                      border: InputBorder.none,
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      suffixIcon: IconButton(
                        icon: Icon(Icons.clear, color: Colors.grey),
                        onPressed: () {
                          searchController.clear();
                          _filterUsers('');
                        },
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(height: 10),
            ],
          ),
        ),
      ),
      body: Stack(
        children: [
          RefreshIndicator(
            color: Colors.red,
            onRefresh: _refreshUsers, // Trigger refresh only on user action
            child: FutureBuilder<List<User>>(
              future: futureUsers,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  return Center(child: Text('Error: ${snapshot.error}'));
                } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return Center(child: Text('No users found'));
                }

                final users = snapshot.data!;
                return SingleChildScrollView(
                  // Wrapping ListView in SingleChildScrollView
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        ListView.builder(
                          shrinkWrap:
                              true, // Ensures the ListView takes only as much height as needed
                          physics:
                              NeverScrollableScrollPhysics(), // Disable internal scrolling
                          itemCount: filteredUsers.length,
                          itemBuilder: (context, index) {
                            final user = filteredUsers[index];
                            return Padding(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 8.0),
                              child: GestureDetector(
                                onTap: () => _showUpdateDialog(user),
                                child: Card(
                                  elevation: 4,
                                  shadowColor: Colors.grey.withOpacity(0.2),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: ListTile(
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 12),
                                    title: Text(
                                      user.name,
                                      style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Text(
                                      'Mobile: ${user.mobile}',
                                      style: TextStyle(color: Colors.grey[700]),
                                    ),
                                    trailing: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        Text(
                                          'Balance:',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey[600],
                                          ),
                                        ),
                                        Text(
                                          '₹${user.balance.toStringAsFixed(2)}',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.green,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                        SizedBox(
                            height:
                                100), // Add space at the bottom for the button
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          Positioned(
            bottom: 30,
            left: 16,
            right: 16,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.redAccent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                padding: EdgeInsets.symmetric(vertical: 16),
                elevation: 6,
              ),
              onPressed: _showCommonWalletUpdateDialog,
              child: Text(
                'Common Wallet Update',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
