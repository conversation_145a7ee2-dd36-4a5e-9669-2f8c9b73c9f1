import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:user/share.dart';

class TodaysOrdersPage extends StatefulWidget {
  final List<dynamic> orders;

  const TodaysOrdersPage({Key? key, required this.orders}) : super(key: key);

  @override
  State<TodaysOrdersPage> createState() => _TodaysOrdersPageState();
}

class _TodaysOrdersPageState extends State<TodaysOrdersPage> {
  final String apiUrl = "https://price.zoomfresh.co.in/api/orders";
  final String token =
      "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

  TextEditingController searchController = TextEditingController();
  List<dynamic> filteredOrders = [];

  @override
  void initState() {
    super.initState();
    filteredOrders = widget.orders;
  }

  void filterOrders(String query) {
    setState(() {
      if (query.isEmpty) {
        filteredOrders = widget.orders;
      } else {
        filteredOrders = widget.orders.where((order) {
          String userName = (order['user_name'] ?? '').toLowerCase();
          String phoneNumber = (order['mobile'] ?? '').toLowerCase();
          String orderid = (order['orders_id'] ?? '').toLowerCase();
          return userName.contains(query.toLowerCase()) ||
              phoneNumber.contains(query.toLowerCase()) ||
              orderid.contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  void showDetailsDialog(int orderId) async {
    try {
      // Fetch order details
      final response = await http.get(
        Uri.parse("https://price.zoomfresh.co.in/api/orders/view/$orderId"),
        headers: {
          'Content-Type': 'application/json',
          "Authorization": token,
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 1 && data['data']['order_items'] != null) {
          List<dynamic> orderItems = data['data']['order_items'];

          // Show dialog with fetched details
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16.0),
                ),
                title: Text(
                  'Ordered Products',
                  style: TextStyle(
                    color: Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                content: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ...orderItems.map((item) {
                        return Card(
                          elevation: 4,
                          margin: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Product Name: ${item['product_name'] ?? 'N/A'}",
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  "Variant: ${item['variant_name'] ?? 'N/A'}",
                                  style: const TextStyle(fontSize: 14),
                                ),
                                Text(
                                  "Cut Method: ${item['cut_method'] ?? 'N/A'}",
                                  style: const TextStyle(fontSize: 14),
                                ),
                                Text(
                                  "Quantity: ${item['quantity'] ?? 'N/A'}",
                                  style: const TextStyle(fontSize: 14),
                                ),
                                Text(
                                  "Price: ₹${item['sub_total'] ?? 'N/A'}",
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.green[800],
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text(
                      "Close",
                      style: TextStyle(color: Colors.blue),
                    ),
                  ),
                ],
              );
            },
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("No items found for this order.")),
          );
        }
      } else {
        throw Exception("Failed to fetch order details.");
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Error: $e")),
      );
    }
  }

  String extractAndAddTime(String status) {
    // Extract the part inside the quotes using a RegExp
    final regex = RegExp(r'\[\[2,"(.*?)"\]\]');
    final match = regex.firstMatch(status);

    // If a match is found, proceed with adding the time
    if (match != null) {
      // Extract the date-time string
      String dateTimeString = match.group(1)!;

      try {
        // Parse the date-time string into a DateTime object
        DateFormat dateFormat = DateFormat("dd-MM-yyyy hh:mm:ssa");
        DateTime parsedDate = dateFormat
            .parse(dateTimeString.toUpperCase()); // Convert AM/PM to uppercase

        // Add 5 hours and 30 minutes
        DateTime updatedDate = parsedDate.add(Duration(hours: 5, minutes: 30));

        // Return the updated date-time in the same format
        return dateFormat.format(updatedDate);
      } catch (e) {
        return "Error parsing date: $e";
      }
    } else {
      return "Invalid status";
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(color: Colors.white),
        title: const Text(
          "Today's Orders",
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.blue,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: searchController,
              decoration: InputDecoration(
                hintText: "Search...",
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
              ),
              onChanged: filterOrders,
            ),
          ),
          Expanded(
            child: filteredOrders.isEmpty
                ? const Center(
                    child: Text(
                      "No orders found.",
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  )
                : ListView.builder(
                    itemCount: filteredOrders.length,
                    itemBuilder: (context, index) {
                      final order = filteredOrders[index];
                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16.0,
                          vertical: 8.0,
                        ),
                        elevation: 4,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.0),
                        ),
                        child: ListTile(
                          leading: const CircleAvatar(
                            backgroundColor: Colors.green,
                            child: Icon(
                              Icons.shopping_cart,
                              color: Colors.white,
                            ),
                          ),
                          contentPadding: const EdgeInsets.all(16.0),
                          title: Text(
                            "Order ID: ${order['order_id']}",
                            style: const TextStyle(
                              color: Colors.blue,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Name: ${order['user_name']}",
                                style: const TextStyle(
                                  color: Colors.black87,
                                  fontSize: 16,
                                ),
                              ),
                              Text(
                                "Address: ${order['address']}",
                                style: const TextStyle(
                                  color: Colors.black87,
                                  fontSize: 16,
                                ),
                              ),
                              Text(
                                "Mobile: ${order['mobile']}",
                                style: const TextStyle(
                                  color: Colors.black87,
                                  fontSize: 16,
                                ),
                              ),
                              Text(
                                "Alternative Number: ${order[''] ?? 'N/A'}",
                                style: const TextStyle(
                                  color: Colors.black,
                                  fontSize: 16,
                                ),
                              ),
                              Text(
                                "Slot: ${order['delivery_time'] ?? 'N/A'}",
                                style: const TextStyle(
                                  color: Colors.black,
                                  fontSize: 16,
                                ),
                              ),
                              Text(
                                "Payment Method: ${order['payment_method']}",
                                style: const TextStyle(
                                  color: Colors.black87,
                                  fontSize: 16,
                                ),
                              ),
                              Text(
                                "Ordered At: ${extractAndAddTime(order['status'])}",
                                style: const TextStyle(
                                  color: Colors.black87,
                                  fontSize: 16,
                                ),
                              ),

// Function to extract the date and time

                              Text(
                                "Total: ₹${order['final_total']}",
                                style: TextStyle(
                                  color: Colors.blue[800],
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(
                                height: 20,
                              ),
                              Row(
                                children: [
                                  ElevatedButton(
                                    onPressed: () {
                                      showDetailsDialog(order['id']);
                                    },
                                    style: ElevatedButton.styleFrom(
                                      foregroundColor: Colors.white,
                                      backgroundColor: Colors.blueAccent,
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                      ),
                                    ),
                                    child: const Text("Products"),
                                  ),
                                  SizedBox(
                                    width: 20,
                                  ),
                                  ElevatedButton(
                                    onPressed: () async {
                                      // Fetch product details for the order
                                      final response = await http.get(
                                        Uri.parse(
                                            "https://price.zoomfresh.co.in/api/orders/view/${order['id']}"),
                                        headers: {
                                          'Content-Type': 'application/json',
                                          "Authorization": token,
                                        },
                                      );

                                      if (response.statusCode == 200) {
                                        final data = json.decode(response.body);
                                        if (data['status'] == 1 &&
                                            data['data']['order_items'] !=
                                                null) {
                                          List<dynamic> orderItems =
                                              data['data']['order_items'];
                                          shareOrderDetails(order, orderItems);
                                        } else {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                                content: Text(
                                                    "No items found for this order.")),
                                          );
                                        }
                                      } else {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                              content: Text(
                                                  "Failed to fetch order details.")),
                                        );
                                      }
                                    },
                                    style: ElevatedButton.styleFrom(
                                      foregroundColor: Colors.white,
                                      backgroundColor: Colors.orange,
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.share,
                                          color: Colors.white,
                                        ),
                                        SizedBox(
                                          width: 10,
                                        ),
                                        const Text("Share"),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(width: 8.0),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
