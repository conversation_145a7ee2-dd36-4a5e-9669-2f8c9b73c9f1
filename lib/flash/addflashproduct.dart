import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:fluttertoast/fluttertoast.dart';

class FlashProduct extends StatefulWidget {
  const FlashProduct({super.key});

  @override
  State<FlashProduct> createState() => _FlashProductState();
}

class _FlashProductState extends State<FlashProduct> {
  late Future<List<Product>> _productsFuture;
  final TextEditingController _searchController = TextEditingController();
  List<Product> _allProducts = [];
  List<Product> _filteredProducts = [];

  @override
  void initState() {
    super.initState();
    _productsFuture = fetchProducts();
    _searchController.addListener(_filterProducts);
  }

  Future<List<Product>> fetchProducts() async {
    try {
      final response = await http.get(
          Uri.parse('https://price.zoomfresh.co.in/api/productlistflashsale'));

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        print('API Response: $jsonResponse');
        print('Response Keys: ${jsonResponse.keys}');

        if (jsonResponse is Map<String, dynamic> &&
            jsonResponse.containsKey('data')) {
          List<dynamic> productList = jsonResponse['data'];
          _allProducts =
              productList.map((data) => Product.fromJson(data)).toList();
          _filteredProducts = List.from(_allProducts);
          return _filteredProducts;
        } else {
          throw Exception('Unexpected response format: No "data" key found');
        }
      } else {
        throw Exception('Failed to load products: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching products: $e');
    }
  }

  Future<void> _updateFlashSaleStatus(Product product, bool isFlash) async {
    final status = isFlash ? '1' : '0';
    final url = 'https://price.zoomfresh.co.in/api/addproducttoflash';
    final headers = {
      'Content-Type': 'application/json',
      'Authorization':
          'Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    };
    final body = json.encode({
      'product_id': product.id,
      'status': status,
    });

    try {
      final response =
          await http.post(Uri.parse(url), headers: headers, body: body);

      if (response.statusCode == 200) {
        print('Success');
        Fluttertoast.showToast(
          msg: isFlash
              ? 'Product added to flash sales'
              : 'Product removed from flash sales',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
      } else {
        Fluttertoast.showToast(
          msg: 'Failed to update product status',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'Error: $e',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
    }
  }

  void _filterProducts() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredProducts = _allProducts
          .where((product) => product.name.toLowerCase().contains(query))
          .toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        backgroundColor: Colors.red, // Set background color to red
        title: Text('Update Flash Products',
            style: TextStyle(color: Colors.white, fontSize: 18)),
        bottom: PreferredSize(
          preferredSize:
              Size.fromHeight(80.0), // Increase height to fit search box
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: TextField(
                  cursorColor: Colors.red,
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search products...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(60.0),
                      borderSide: BorderSide(
                          color: Colors.white), // Border color when not focused
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(60.0),
                      borderSide: BorderSide(
                          color: Colors.white), // Border color when focused
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(60.0),
                      borderSide: BorderSide(
                          color: Colors.white), // Border color when enabled
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    suffixIcon: Icon(Icons.search, color: Colors.red),
                  ),
                ),
              ),
              SizedBox(height: 15),
            ],
          ),
        ),
      ),
      body: FutureBuilder<List<Product>>(
        future: _productsFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(
                child: CircularProgressIndicator(
              color: Colors.red,
              strokeWidth: 2,
            ));
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return Center(child: Text('No products available'));
          }

          return ListView.builder(
            itemCount: _filteredProducts.length,
            itemBuilder: (context, index) {
              final product = _filteredProducts[index];
              return ListTile(
                contentPadding: EdgeInsets.all(8.0),
                title: Container(
                  padding: EdgeInsets.all(10.0),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.red),
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.network(
                          'https://price.zoomfresh.co.in/${product.image}',
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Image.asset(
                              'images/p.png',
                              color:
                                  Colors.grey, // Path to your placeholder image
                              width: 80,
                              height: 80,
                              fit: BoxFit.cover,
                            );
                          },
                          loadingBuilder: (context, child, progress) {
                            if (progress == null) {
                              return child;
                            } else {
                              return Center(
                                child: CircularProgressIndicator(
                                  color: Colors.red,
                                  strokeWidth: 2,
                                ),
                              );
                            }
                          },
                        ),
                      ),
                      SizedBox(width: 10),
                      Expanded(
                        child: Text(product.name),
                      ),
                      Switch(
                        value: product.isFlash == '1',
                        onChanged: (bool value) {
                          setState(() {
                            product.isFlash = value ? '1' : '0';
                          });
                          _updateFlashSaleStatus(product, value);
                        },
                        activeColor: Colors.red,
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

class Product {
  String id;
  String isFlash;
  String name;
  String image;

  Product({
    required this.id,
    required this.isFlash,
    required this.name,
    required this.image,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'].toString(),
      isFlash: json['is_flash'].toString(),
      name: json['name'],
      image: json['image'],
    );
  }
}
