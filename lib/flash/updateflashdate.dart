import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';

class UpdateFlashdate extends StatefulWidget {
  @override
  _UpdateFlashdateState createState() => _UpdateFlashdateState();
}

class _UpdateFlashdateState extends State<UpdateFlashdate> {
  DateTime? startDate;
  DateTime? endDate;
  TimeOfDay? startTime;
  TimeOfDay? endTime;
  bool isLoading = false;

  void pickDate(BuildContext context, bool isStart) async {
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2100),
    );

    if (picked != null) {
      setState(() {
        if (isStart) {
          startDate = picked;
        } else {
          endDate = picked;
        }
      });
    }
  }

  void pickTime(BuildContext context, bool isStart) async {
    TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      builder: (BuildContext context, Widget? child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isStart) {
          startTime = picked;
        } else {
          endTime = picked;
        }
      });
    }
  }

  String formatTimeOfDay(TimeOfDay timeOfDay) {
    final now = DateTime.now();
    final dateTime = DateTime(
        now.year, now.month, now.day, timeOfDay.hour, timeOfDay.minute);
    final formattedTime = DateFormat('HH:mm').format(dateTime);
    print('Formatted Time: $formattedTime'); // Debugging: Print formatted time
    return formattedTime; // 24-hour format
  }

  String formatDate(DateTime date) {
    final DateFormat formatter =
        DateFormat('dd-MM-yyyy'); // Customize format as needed
    final formattedDate = formatter.format(date);
    print('Formatted Date: $formattedDate'); // Debugging: Print formatted date
    return formattedDate;
  }

  Future<void> updateFlashDateTime() async {
    if (startDate == null ||
        endDate == null ||
        startTime == null ||
        endTime == null) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Error'),
          content: Text('Please choose all date and time fields.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('OK', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );
      return;
    }

    setState(() {
      isLoading = true;
    });

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Center(
        child: CircularProgressIndicator(color: Colors.red, strokeWidth: 2),
      ),
    );

    final url = Uri.parse('https://price.zoomfresh.co.in/api/updateflashdate');
    final headers = {
      'Content-Type': 'application/json',
      'Authorization':
          'Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', // Replace with your actual access token
    };
    final body = jsonEncode({
      'start_date': formatDate(startDate!),
      'start_time': formatTimeOfDay(startTime!),
      'end_date': formatDate(endDate!),
      'end_time': formatTimeOfDay(endTime!),
    });

    print('Request Body: $body'); // Debugging: Print request body

    final response = await http.post(url, headers: headers, body: body);

    setState(() {
      isLoading = false;
    });

    Navigator.pop(context); // Remove the loader

    if (response.statusCode == 200) {
      print(response.body);
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Success'),
          content: Text('Updated date and time successfully.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('OK', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );
    } else {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Error'),
          content: Text('Failed to update date and time. ${response.body}'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('OK'),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        title: Text('Flash Sale', style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.red,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Start Date:', style: TextStyle(fontSize: 16)),
            Row(
              children: [
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                  ),
                  onPressed: () => pickDate(context, true),
                  child: Text(
                    startDate != null
                        ? formatDate(startDate!)
                        : 'Choose Start Date',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                SizedBox(width: 10),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                  ),
                  onPressed: () => pickTime(context, true),
                  child: Text(
                    startTime != null
                        ? formatTimeOfDay(startTime!)
                        : 'Choose Start Time',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20),
            Text('End Date:', style: TextStyle(fontSize: 16)),
            Row(
              children: [
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                  ),
                  onPressed: () => pickDate(context, false),
                  child: Text(
                    endDate != null ? formatDate(endDate!) : 'Choose End Date',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                SizedBox(width: 10),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                  ),
                  onPressed: () => pickTime(context, false),
                  child: Text(
                    endTime != null
                        ? formatTimeOfDay(endTime!)
                        : 'Choose End Time',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20),
            Center(
              child: ElevatedButton(
                onPressed: updateFlashDateTime,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  padding:
                      EdgeInsets.symmetric(vertical: 10.0, horizontal: 50.0),
                  textStyle: TextStyle(
                    fontSize: 18,
                  ),
                ),
                child: Text(
                  'UPDATE DATE & TIME',
                  style: TextStyle(color: Colors.white, fontFamily: 'Neuton'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
