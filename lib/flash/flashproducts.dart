import 'dart:convert';
import 'package:http/http.dart' as http;

import 'package:flutter/material.dart';
import 'package:user/flashproductservice.dart';
import 'package:user/flashproductsmodel.dart';
import 'package:user/flash/addflashproduct.dart';
import 'package:user/flash/updateflashdate.dart';

class FlashSaleScreen extends StatefulWidget {
  @override
  _FlashSaleScreenState createState() => _FlashSaleScreenState();
}

class _FlashSaleScreenState extends State<FlashSaleScreen> {
  late Future<List<FlashSaleProduct>> _productsFuture;
  late Future<Map<String, String>> _flashSaleDatesFuture;

  @override
  void initState() {
    super.initState();
    // Initialize the futures once
    _productsFuture = fetchFlashSaleProducts();
    _flashSaleDatesFuture = fetchFlashSaleDates();
  }

  Future<void> _refreshData() async {
    // Only refresh the data when explicitly requested (e.g., pull-to-refresh)
    setState(() {
      _productsFuture = fetchFlashSaleProducts();
      _flashSaleDatesFuture = fetchFlashSaleDates();
    });
  }

  Future<Map<String, String>> fetchFlashSaleDates() async {
    final url =
        'https://price.zoomfresh.co.in/api/getflashdate'; // Replace with your actual URL
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer <Your_Token>',
    };

    try {
      final response = await http.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['status'] == 'success') {
          final data = responseData['data'][0];
          return {
            "startDate": data["start_date"],
            "startTime": data["start_time"],
            "endDate": data["end_date"],
            "endTime": data["end_time"],
          };
        } else {
          throw Exception('Failed to load flash sale dates');
        }
      } else {
        throw Exception('Failed to load flash sale dates');
      }
    } catch (e) {
      throw Exception('Error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        backgroundColor: Colors.red,
        title:
            Text('Flash Sale Products', style: TextStyle(color: Colors.white)),
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData, // Calls _refreshData when pulled down
        child: ListView(
          children: [
            FutureBuilder<Map<String, String>>(
              future: _flashSaleDatesFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Center(
                        child: CircularProgressIndicator(
                            color: Colors.red, strokeWidth: 2)),
                  );
                } else if (snapshot.hasError) {
                  return Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Center(child: Text('Error: ${snapshot.error}')),
                  );
                } else if (snapshot.hasData) {
                  final data = snapshot.data!;
                  return Container(
                    padding: EdgeInsets.all(16.0),
                    margin:
                        EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(12.0),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.3),
                          spreadRadius: 2,
                          blurRadius: 5,
                          offset: Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Flash Sale Dates & Times',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),
                        SizedBox(height: 12.0),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildDateTimeLabel(
                                'Start Date', data["startDate"] ?? 'N/A'),
                            _buildDateTimeLabel(
                                'Start Time', data["startTime"] ?? 'N/A'),
                          ],
                        ),
                        SizedBox(height: 8.0),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildDateTimeLabel(
                                'End Date', data["endDate"] ?? 'N/A'),
                            _buildDateTimeLabel(
                                'End Time', data["endTime"] ?? 'N/A'),
                          ],
                        ),
                      ],
                    ),
                  );
                } else {
                  return Padding(
                    padding: const EdgeInsets.all(16.0),
                    child:
                        Center(child: Text('No flash sale dates available.')),
                  );
                }
              },
            ),
            SizedBox(height: 10),
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child:
                    Text('Flash Sale Products', style: TextStyle(fontSize: 20)),
              ),
            ),
            SizedBox(height: 10),
            FutureBuilder<List<FlashSaleProduct>>(
              future: _productsFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(
                      child: CircularProgressIndicator(
                          color: Colors.red, strokeWidth: 2));
                } else if (snapshot.hasError) {
                  return Center(child: Text('Error: ${snapshot.error}'));
                } else if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                  final products = snapshot.data!;
                  return Column(
                    children: products.map((product) {
                      return Container(
                        margin: EdgeInsets.symmetric(
                            vertical: 8.0, horizontal: 16.0),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.red, width: 1.0),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        child: ListTile(
                          contentPadding: EdgeInsets.all(8.0),
                          leading: ClipRRect(
                            borderRadius: BorderRadius.circular(5),
                            child: Image.network(
                              'https://price.zoomfresh.co.in/${product.image}',
                              errorBuilder: (context, error, stackTrace) {
                                return Image.asset(
                                  'images/p.png', // Path to your placeholder image
                                  color: Colors.grey,
                                  width: 80,
                                  height: 80,
                                  fit: BoxFit.cover,
                                );
                              },
                            ),
                          ),
                          title: Text(product.name),
                        ),
                      );
                    }).toList(),
                  );
                } else {
                  return Center(
                      child: Text('No flash sale products available.'));
                }
              },
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomAppBar(
        color: Colors.white,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => FlashProduct()),
                );
              },
              child: Text('Add FlashSale Products',
                  style: TextStyle(color: Colors.white)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                padding: EdgeInsets.symmetric(vertical: 16.0, horizontal: 10.0),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => UpdateFlashdate()),
                );
              },
              child: Text('Update Date&Time',
                  style: TextStyle(color: Colors.white)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                padding: EdgeInsets.symmetric(vertical: 16.0, horizontal: 15.0),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateTimeLabel(String label, String value) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.red.shade700,
            ),
          ),
          SizedBox(height: 4.0),
          Text(
            value,
            style: TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }
}
