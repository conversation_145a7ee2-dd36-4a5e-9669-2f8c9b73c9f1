import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:user/usermodel.dart';

Future<List<User>> fetchUsers() async {
  final response = await http.get(
    Uri.parse('https://price.zoomfresh.co.in/api/userlist'),
    headers: {
      'Authorization': 'Bearer YOUR_API_TOKEN',
      'Content-Type': 'application/json',
    },
  );

  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    final users = (data['data'] as List)
        .map((userJson) => User.fromJson(userJson))
        .toList();
    return users;
  } else {
    throw Exception('Failed to load user list');
  }
}
