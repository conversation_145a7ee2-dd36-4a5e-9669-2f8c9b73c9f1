class FlashSaleProduct {
  final int id;
  final String name;
  final String image;
  final String description;

  FlashSaleProduct({
    required this.id,
    required this.name,
    required this.image,
    required this.description,
  });

  factory FlashSaleProduct.fromJson(Map<String, dynamic> json) {
    return FlashSaleProduct(
      id: json['id'],
      name: json['name'],
      image: json['image'],
      description: json['description'],
    );
  }
}
