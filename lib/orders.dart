import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:user/share.dart';
import 'package:user/todaysorders.dart';

class MyOrders extends StatefulWidget {
  const MyOrders({super.key});

  @override
  State<MyOrders> createState() => _MyOrdersState();
}

class _MyOrdersState extends State<MyOrders> {
  final String apiUrl = "https://price.zoomfresh.co.in/api/orders";
  final String token =
      "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
  List<dynamic> sellers = [];
  List<dynamic> filteredSellers = [];
  bool isLoading = true;

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    fetchOrders();
    _searchController.addListener(_onSearchChanged);
  }

  Future<void> _refresh() async {
    setState(() {
      isLoading = true;
    });
    await fetchOrders(); // Re-fetch orders when refreshed
  }

  void _onSearchChanged() {
    String query = _searchController.text.toLowerCase();
    setState(() {
      filteredSellers = sellers.where((seller) {
        String userName = (seller['user_name'] ?? '').toLowerCase();
        String phoneNumber = (seller['mobile'] ?? '').toLowerCase();
        String orderid = (seller['orders_id'] ?? '').toLowerCase();
        return userName.contains(query) ||
            phoneNumber.contains(query) ||
            orderid.contains(query);
      }).toList();
    });
  }

  Future<void> fetchOrders() async {
    try {
      final response = await http.get(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
          "Authorization": token,
        },
      );

      if (response.statusCode == 200) {
        print(response.body);
        final data = json.decode(response.body);
        if (data['status'] == 1) {
          setState(() {
            sellers = data['data']['orders'];
            filteredSellers =
                sellers; // Set filtered list to all orders initially
            isLoading = false;
          });
        } else {
          throw Exception("Failed to load orders.");
        }
      } else {
        throw Exception("Failed to fetch API.");
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text("Error: $e")));
    }
  }

  @override
  void dispose() {
    _searchController.dispose(); // Dispose the controller when no longer needed
    super.dispose();
  }

  void showDetailsDialog(int orderId) async {
    try {
      // Fetch order details
      final response = await http.get(
        Uri.parse("https://price.zoomfresh.co.in/api/orders/view/$orderId"),
        headers: {
          'Content-Type': 'application/json',
          "Authorization": token,
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 1 && data['data']['order_items'] != null) {
          List<dynamic> orderItems = data['data']['order_items'];

          // Show dialog with fetched details
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16.0),
                ),
                title: Text(
                  'Ordered Products',
                  style: TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                content: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ...orderItems.map((item) {
                        return Card(
                          elevation: 4,
                          margin: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Product Name: ${item['product_name'] ?? 'N/A'}",
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  "Variant: ${item['variant_name'] ?? 'N/A'}",
                                  style: const TextStyle(fontSize: 14),
                                ),
                                Text(
                                  "Cut Method: ${item['cut_method'] ?? 'N/A'}",
                                  style: const TextStyle(fontSize: 14),
                                ),
                                Text(
                                  "Quantity: ${item['quantity'] ?? 'N/A'}",
                                  style: const TextStyle(fontSize: 14),
                                ),
                                Text(
                                  "Price: ₹${item['sub_total'] ?? 'N/A'}",
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.green[800],
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text(
                      "Close",
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              );
            },
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("No items found for this order.")),
          );
        }
      } else {
        throw Exception("Failed to fetch order details.");
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Error: $e")),
      );
    }
  }

  List<dynamic> getTodaysOrders() {
    DateTime today = DateTime.now();
    return sellers.where((order) {
      DateTime orderDate = DateTime.parse(order['created_at']).toLocal();
      // Create a new DateTime object with only the year, month, and day
      DateTime orderDay =
          DateTime(orderDate.year, orderDate.month, orderDate.day);
      DateTime currentDay = DateTime(today.year, today.month, today.day);

      return orderDay == currentDay; // Compare only the date part
    }).toList();
  }

  String extractAndAddTime(String status) {
    // Extract the part inside the quotes using a RegExp
    final regex = RegExp(r'\[\[2,"(.*?)"\]\]');
    final match = regex.firstMatch(status);

    // If a match is found, proceed with adding the time
    if (match != null) {
      // Extract the date-time string
      String dateTimeString = match.group(1)!;

      try {
        // Parse the date-time string into a DateTime object
        DateFormat dateFormat = DateFormat("dd-MM-yyyy hh:mm:ssa");
        DateTime parsedDate = dateFormat
            .parse(dateTimeString.toUpperCase()); // Convert AM/PM to uppercase

        // Add 5 hours and 30 minutes
        DateTime updatedDate = parsedDate.add(Duration(hours: 5, minutes: 30));

        // Return the updated date-time in the same format
        return dateFormat.format(updatedDate);
      } catch (e) {
        return "Error parsing date: $e";
      }
    } else {
      return "Invalid status";
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        title: const Text("Orders", style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.red,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Search...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                prefixIcon: const Icon(Icons.search),
              ),
            ),
          ),
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator())
                : filteredSellers.isEmpty
                    ? const Center(
                        child: Text(
                          "No orders available.",
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey,
                          ),
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _refresh,
                        child: ListView.builder(
                          itemCount: filteredSellers.length,
                          itemBuilder: (context, index) {
                            final seller = filteredSellers[index];
                            return Card(
                              margin: const EdgeInsets.symmetric(
                                horizontal: 16.0,
                                vertical: 8.0,
                              ),
                              elevation: 4,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              child: ListTile(
                                contentPadding: const EdgeInsets.all(16.0),
                                leading: CircleAvatar(
                                  backgroundColor: Colors.green,
                                  child: const Icon(
                                    Icons.shopping_cart,
                                    color: Colors.white,
                                  ),
                                ),
                                title: Text(
                                  "Order ID: ${seller['order_id'] ?? 'N/A'}",
                                  style: const TextStyle(
                                    color: Colors.red,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                subtitle: Padding(
                                  padding: const EdgeInsets.only(top: 8.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Name: ${seller['user_name'] ?? 'N/A'}",
                                        style: const TextStyle(
                                          color: Colors.black87,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        "Address: ${seller['address'] ?? 'N/A'}",
                                        style: const TextStyle(
                                          color: Colors.black,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        "Mobile: ${seller['mobile'] ?? 'N/A'}",
                                        style: const TextStyle(
                                          color: Colors.black,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        "Alternative Number: ${seller[''] ?? 'N/A'}",
                                        style: const TextStyle(
                                          color: Colors.black,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        "Slot: ${seller['delivery_time'] ?? 'N/A'}",
                                        style: const TextStyle(
                                          color: Colors.black,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        "Payment Method: ${seller['payment_method'] ?? 'N/A'}",
                                        style: const TextStyle(
                                          color: Colors.black,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        "Ordered At: ${extractAndAddTime(seller['status'])}",
                                        style: const TextStyle(
                                          color: Colors.black87,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        "Total: ₹${seller['final_total'] ?? 'N/A'}",
                                        style: TextStyle(
                                          color: Colors.red[800],
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      SizedBox(
                                        height: 20,
                                      ),
                                      Row(
                                        children: [
                                          ElevatedButton(
                                            onPressed: () {
                                              showDetailsDialog(seller['id']);
                                            },
                                            style: ElevatedButton.styleFrom(
                                              foregroundColor: Colors.white,
                                              backgroundColor: Colors.red,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(8.0),
                                              ),
                                            ),
                                            child: const Text("Products"),
                                          ),
                                          SizedBox(
                                            width: 20,
                                          ),
                                          ElevatedButton(
                                            onPressed: () async {
                                              // Fetch product details for the order
                                              final response = await http.get(
                                                Uri.parse(
                                                    "https://price.zoomfresh.co.in/api/orders/view/${seller['id']}"),
                                                headers: {
                                                  'Content-Type':
                                                      'application/json',
                                                  "Authorization": token,
                                                },
                                              );

                                              if (response.statusCode == 200) {
                                                final data =
                                                    json.decode(response.body);
                                                if (data['status'] == 1 &&
                                                    data['data']
                                                            ['order_items'] !=
                                                        null) {
                                                  List<dynamic> orderItems =
                                                      data['data']
                                                          ['order_items'];
                                                  shareOrderDetails(
                                                      seller, orderItems);
                                                } else {
                                                  ScaffoldMessenger.of(context)
                                                      .showSnackBar(
                                                    SnackBar(
                                                        content: Text(
                                                            "No items found for this order.")),
                                                  );
                                                }
                                              } else {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                      content: Text(
                                                          "Failed to fetch order details.")),
                                                );
                                              }
                                            },
                                            style: ElevatedButton.styleFrom(
                                              foregroundColor: Colors.white,
                                              backgroundColor: Colors.orange,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(8.0),
                                              ),
                                            ),
                                            child: Row(
                                              children: [
                                                Icon(
                                                  Icons.share,
                                                  color: Colors.white,
                                                ),
                                                SizedBox(
                                                  width: 10,
                                                ),
                                                const Text("Share"),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: Container(
        width: 200.0, // Set the width to 200
        height: 50.0, // Adjust the height as needed
        child: FloatingActionButton(
          onPressed: () {
            // Navigate to a new page showing today's orders
            List<dynamic> todaysOrders = getTodaysOrders();
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => TodaysOrdersPage(orders: todaysOrders),
              ),
            );
          },
          backgroundColor: Colors.blue, // Customize the FAB color
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
                12), // Make it a perfect rectangle with no rounded corners
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.calendar_month,
                color: Colors.white,
              ),
              SizedBox(
                width: 10,
              ),
              const Text(
                'Today\'s Orders',
                style: TextStyle(
                    fontSize: 16.0, // Adjust the font size as needed
                    color: Colors.white,
                    fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
