import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:share_plus/share_plus.dart';

class SubscribedUsersScreen extends StatefulWidget {
  const SubscribedUsersScreen({super.key});

  @override
  State<SubscribedUsersScreen> createState() => _SubscribedUsersScreenState();
}

class _SubscribedUsersScreenState extends State<SubscribedUsersScreen> {
  late Future<List<User>> futureUsers;
  late List<User> allUsers = [];
  List<User> filteredUsers = [];
  TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    futureUsers = fetchUsers();
    futureUsers.then((users) {
      setState(() {
        allUsers = users;
        filteredUsers = users; // Initially, display all users
      });
    });
  }

  // Fetch users
  Future<List<User>> fetchUsers() async {
    final url = 'https://price.zoomfresh.co.in/api/subscribeduserlist';
    final response = await http.get(
      Uri.parse(url),
      headers: {
        'Authorization': 'Bearer YOUR_API_TOKEN',
        'Content-Type': 'application/json',
      },
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final users = (data['data'] as List)
          .map((userJson) => User.fromJson(userJson))
          .toList();
      return users;
    } else {
      throw Exception('Failed to load user list');
    }
  }

  // Filter users by name
  void _filterUsers(String query) {
    final results = allUsers.where((user) {
      final userName = user.name.toLowerCase();
      final input = query.toLowerCase();
      return userName.contains(input);
    }).toList();

    setState(() {
      filteredUsers = results;
    });
  }

  // Handle Date Picker and Filter Users by Selected Date
  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2101),
    );

    if (picked != null) {
      final formattedDate = DateFormat('dd-MM-yyyy').format(picked);
      _filterUsersByDate(formattedDate);
    }
  }

  // Filter users by selected date
  void _filterUsersByDate(String selectedDate) {
    final results = allUsers.where((user) {
      final userDate =
          user.date; // Assuming `date` is a string like 'dd-MM-yyyy'
      return userDate == selectedDate;
    }).toList();

    setState(() {
      filteredUsers = results;
    });
  }

  Future<void> _refreshUsers() async {
    futureUsers = fetchUsers();
    futureUsers.then((users) {
      setState(() {
        allUsers = users;
        filteredUsers = users; // Reset filtered users
      });
    });
  }

  void _shareUserDetails(User user) {
    final details = '''
Urgent: Subscription Renewal Required

Dear Customer,

Your current subscription plan is set to expire on ${user.enddate}. To continue enjoying free delivery benefits, kindly renew your subscription as soon as possible.

For your reference:

Subscription Details:
1. Name: ${user.name}
2. Mobile Number: ${user.mobile}
3. Plan: ${user.plan}
4. Start Date: ${user.date}
5. End Date: ${user.enddate}

Thank you for your prompt attention to this matter.
''';
    Share.share(details);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        title: Text(
          'Subscribed Users',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.blue,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize:
              Size.fromHeight(70), // Adjust the height for the search box
          child: Column(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(
                              25), // Circular edges for the search box
                        ),
                        child: TextField(
                          controller: searchController,
                          onChanged: _filterUsers,
                          decoration: InputDecoration(
                            hintText: 'Search users by name',
                            hintStyle: TextStyle(color: Colors.grey),
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            suffixIcon: IconButton(
                              icon: Icon(Icons.clear, color: Colors.grey),
                              onPressed: () {
                                searchController.clear();
                                _filterUsers('');
                              },
                            ),
                          ),
                        ),
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.calendar_today, color: Colors.white),
                      onPressed: _selectDate, // Open the date picker
                    ),
                  ],
                ),
              ),
              SizedBox(height: 10),
            ],
          ),
        ),
      ),
      body: Stack(
        children: [
          RefreshIndicator(
            color: Colors.white,
            backgroundColor: Colors.blue,
            onRefresh: _refreshUsers,
            child: FutureBuilder<List<User>>(
              future: futureUsers,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(
                      child: CircularProgressIndicator(color: Colors.blue));
                } else if (snapshot.hasError) {
                  return Center(
                    child: Text(
                      'Error: ${snapshot.error}',
                      style: TextStyle(color: Colors.white, fontSize: 18),
                    ),
                  );
                }

                return filteredUsers.isEmpty
                    ? Center(
                        child: Text(
                          'No users found',
                          style: TextStyle(color: Colors.white, fontSize: 18),
                        ),
                      )
                    : Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: ListView.builder(
                          itemCount: filteredUsers.length,
                          itemBuilder: (context, index) {
                            final user = filteredUsers[index];
                            return Padding(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 8.0),
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [Colors.white, Colors.blue.shade50],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.1),
                                      blurRadius: 10,
                                      offset: Offset(0, 5),
                                    ),
                                  ],
                                ),
                                child: ListTile(
                                  leading: CircleAvatar(
                                    backgroundColor: Colors.blue.shade200,
                                    child:
                                        Icon(Icons.person, color: Colors.white),
                                  ),
                                  title: Text(
                                    user.name,
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  subtitle: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Subscription Plan: ${user.plan}',
                                        style: TextStyle(
                                          color: Colors.blue,
                                          fontSize: 17,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      Text(
                                        'Mobile: ${user.mobile}',
                                        style: TextStyle(
                                          color: Colors.grey.shade800,
                                          fontSize: 14,
                                        ),
                                      ),
                                      Text(
                                        'Date: ${user.date}',
                                        style: TextStyle(
                                          color: Colors.grey.shade800,
                                          fontSize: 14,
                                        ),
                                      ),
                                      Text(
                                        'EndDate: ${user.enddate}',
                                        style: TextStyle(
                                          color: Colors.grey.shade800,
                                          fontSize: 14,
                                        ),
                                      ),
                                      Text(
                                        'Amount: ₹${user.balance.toStringAsFixed(2)}',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ],
                                  ),
                                  trailing: IconButton(
                                    icon: Icon(Icons.share, color: Colors.blue),
                                    onPressed: () => _shareUserDetails(user),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class User {
  final int id;
  final String name;
  final double balance;
  final String mobile;
  final String date;
  final String enddate;
  final String plan;

  User({
    required this.id,
    required this.name,
    required this.balance,
    required this.mobile,
    required this.date,
    required this.enddate,
    required this.plan,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      balance: json['subscription_price'] != null
          ? double.tryParse(json['subscription_price'].toString()) ?? 0.0
          : 0.0,
      mobile: json['mobile'],
      date: json['subscription_startdate'] ?? '',
      enddate: json['subscription_enddate'] ?? '',
      plan: json['title'] ?? '',
    );
  }
}
