import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:user/subscribeduserslist.dart';

class Subscription extends StatefulWidget {
  const Subscription({super.key});

  @override
  State<Subscription> createState() => _SubscriptionState();
}

class _SubscriptionState extends State<Subscription> {
  late Future<List<Map<String, dynamic>>> _subscriptionPlans;

  @override
  void initState() {
    super.initState();
    _subscriptionPlans = fetchSubscriptionPlans();
  }

  Future<List<Map<String, dynamic>>> fetchSubscriptionPlans() async {
    try {
      final response = await http.get(
        Uri.parse('https://price.zoomfresh.co.in/api/subscription'),
      );

      if (response.statusCode == 200) {
        final List<dynamic> responseData =
            json.decode(response.body)['subscriptions'];

        // Ensure the value is properly parsed and handle null values
        return responseData.map((item) {
          return {
            'id': item['package_id'],
            'title': item['title'],
            'price': item['price'],
            // Check if number_of_month is null, and handle it correctly
            'number_of_month': item['number_of_month'] != null
                ? item['number_of_month'] is int
                    ? item['number_of_month']
                    : int.tryParse(item['number_of_month'].toString()) ?? 0
                : 0,
          };
        }).toList();
      } else {
        throw Exception('Failed to load subscription plans');
      }
    } catch (e) {
      print(e);
      throw Exception('Failed to load subscription plans');
    }
  }

  Future<void> addSubscription(
      {required String title,
      required String price,
      required String months}) async {
    final response = await http.post(
      Uri.parse('https://price.zoomfresh.co.in/api/addsubscription'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'title': title,
        'price': price,
        'number_of_month': months, // Add months to the body
      }),
    );

    if (response.statusCode == 200) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Subscription added successfully')),
      );
      setState(() {
        _subscriptionPlans = fetchSubscriptionPlans(); // Refresh the list
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to add subscription')),
      );
    }
  }

  Future<void> editSubscription(
      {required String title,
      required String price,
      required String months,
      required String id}) async {
    final response = await http.post(
      Uri.parse('https://price.zoomfresh.co.in/api/addsubscription'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'title': title,
        'price': price,
        'number_of_month': months, // Add months to the body
        'id': id,
      }),
    );

    if (response.statusCode == 200) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Subscription edited successfully')),
      );
      setState(() {
        _subscriptionPlans = fetchSubscriptionPlans(); // Refresh the list
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to edit subscription')),
      );
    }
  }

  void showEditSubscriptionDialog(String id, String currentTitle,
      String currentPrice, String currentMonths) {
    final TextEditingController titleController =
        TextEditingController(text: currentTitle);
    final TextEditingController priceController =
        TextEditingController(text: currentPrice);
    final TextEditingController monthsController =
        TextEditingController(text: currentMonths);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Subscription'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: titleController,
              decoration: const InputDecoration(labelText: 'Title'),
            ),
            TextField(
              controller: priceController,
              decoration: const InputDecoration(labelText: 'Price'),
              keyboardType: TextInputType.number,
            ),
            TextField(
              controller: monthsController,
              decoration: const InputDecoration(labelText: 'Number of Months'),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
                foregroundColor: Colors.black, backgroundColor: Colors.yellow),
            onPressed: () async {
              Navigator.pop(context); // Close dialog
              await editSubscription(
                title: titleController.text,
                price: priceController.text,
                months: monthsController.text,
                id: id,
              );
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  void showAddSubscriptionDialog() {
    final TextEditingController titleController = TextEditingController();
    final TextEditingController priceController = TextEditingController();
    final TextEditingController monthsController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Subscription'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: titleController,
              decoration: const InputDecoration(labelText: 'Title'),
            ),
            TextField(
              controller: priceController,
              decoration: const InputDecoration(labelText: 'Price'),
              keyboardType: TextInputType.number,
            ),
            TextField(
              controller: monthsController,
              decoration: const InputDecoration(labelText: 'Number of Months'),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white, backgroundColor: Colors.green),
            onPressed: () async {
              Navigator.pop(context); // Close dialog
              await addSubscription(
                title: titleController.text,
                price: priceController.text,
                months: monthsController.text,
              );
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Future<void> deleteSubscription(String id) async {
    final response = await http.post(
      Uri.parse('https://price.zoomfresh.co.in/api/deletesubscription'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({'id': id}),
    );

    if (response.statusCode == 200) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Subscription deleted successfully')),
      );
      setState(() {
        _subscriptionPlans = fetchSubscriptionPlans(); // Refresh the list
      });
    } else {
      print(response.body);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to delete subscription')),
      );
    }
  }

  void showDeleteConfirmationDialog(String id) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Subscription'),
        content:
            const Text('Are you sure you want to delete this subscription?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red, foregroundColor: Colors.white),
            onPressed: () async {
              Navigator.pop(context); // Close dialog
              await deleteSubscription(id);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanCard(Map<String, dynamic> plan) {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.blue,
          borderRadius: BorderRadius.circular(10),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Plan Title
            Text(
              plan['title'],
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            // Plan Price
            Text(
              '₹${plan['price']}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            // Number of Months
            Text(
              'Duration: ${plan['number_of_month'] ?? 'Not available'} months',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),

            const SizedBox(height: 10),
            // Actions Row (Edit and Delete)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Edit Option
                Column(
                  children: [
                    GestureDetector(
                      onTap: () => showEditSubscriptionDialog(
                        plan['id'].toString(),
                        plan['title'],
                        plan['price'].toString(),
                        plan['number_of_month']
                            .toString(), // Pass months to edit dialog
                      ),
                      child: const CircleAvatar(
                        backgroundColor: Colors.amber,
                        child: Icon(Icons.edit, color: Colors.white),
                      ),
                    ),
                    const SizedBox(height: 5),
                    const Text(
                      'Edit',
                      style: TextStyle(color: Colors.white, fontSize: 10),
                    ),
                  ],
                ),
                // Delete Option
                Column(
                  children: [
                    GestureDetector(
                      onTap: () =>
                          showDeleteConfirmationDialog(plan['id'].toString()),
                      child: const CircleAvatar(
                        backgroundColor: Colors.red,
                        child: Icon(Icons.delete_rounded, color: Colors.white),
                      ),
                    ),
                    const SizedBox(height: 5),
                    const Text(
                      'Delete',
                      style: TextStyle(color: Colors.white, fontSize: 10),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          title: const Text('Subscription Plans'),
          foregroundColor: Colors.black,
        ),
        body: Column(
          children: [
            Expanded(
              child: FutureBuilder<List<Map<String, dynamic>>>(
                future: _subscriptionPlans,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (snapshot.hasError) {
                    return const Center(
                        child: Text('Failed to load subscriptions'));
                  } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return const Center(
                        child: Text('No subscription plans available'));
                  }

                  final plans = snapshot.data!;
                  return GridView.builder(
                    padding: const EdgeInsets.all(16),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 0.8,
                    ),
                    itemCount: plans.length,
                    itemBuilder: (context, index) {
                      final plan = plans[index];
                      return _buildPlanCard(plan);
                    },
                  );
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(5),
              child: Row(
                children: [
                  SizedBox(
                    width: 5,
                  ),
                  ElevatedButton(
                    onPressed: () {
                      // Navigate to subscribed user list
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => SubscribedUsersScreen()),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Colors.white,
                      backgroundColor: Colors.blue,
                      minimumSize: const Size(170, 50), // Set width and height
                    ),
                    child: const Text(
                      'Subscribed Users List',
                      style:
                          TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                  ),
                  const SizedBox(width: 5),
                  ElevatedButton(
                    onPressed: () => showAddSubscriptionDialog(),
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Colors.white,
                      backgroundColor: Colors.green,
                      minimumSize: const Size(170, 50), // Set width and height
                    ),
                    child: const Text(
                      'Add Subscription',
                      style:
                          TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ));
  }
}
