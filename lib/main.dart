import 'dart:convert';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:user/firebase_options.dart';
import 'package:user/push_notifications.dart';
import 'package:user/splash.dart';

// The navigator key is used to navigate without a build context.
final navigatorKey = GlobalKey<NavigatorState>();

// This function MUST be a top-level function (not inside a class).
// It's the entry point for messages when the app is in the background or terminated.
@pragma('vm:entry-point')
Future<void> _firebaseBackgroundMessageHandler(RemoteMessage message) async {
  // If you're going to use other Firebase services in the background, like Firestore,
  // make sure you call `initializeApp` before using them.
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  if (message.notification != null) {
    print("Handling a background message: ${message.messageId}");
    // Here you can perform background tasks, like saving data.
  }
}

void main() async {
  // 1. Ensure Flutter bindings are initialized
  WidgetsFlutterBinding.ensureInitialized();

  // 2. Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // 3. Set up the background message handler
  FirebaseMessaging.onBackgroundMessage(_firebaseBackgroundMessageHandler);

  // 4. Initialize all notification services (Push and Local) ONCE.
  await PushNotifications.init();
  if (!kIsWeb) {
    // Local notifications are not applicable for web
    await PushNotifications.localNotiInit();
  }

  // 5. Set up all listeners for handling user interaction with notifications.
  _setupFirebaseMessagingListeners();

  runApp(const MyApp());
}

/// Sets up listeners for foreground, background-tapped, and terminated-tapped notifications.
void _setupFirebaseMessagingListeners() {
  // Handles messages that arrive when the app is in the foreground.
  FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    print("Got a message whilst in the foreground!");
    print("Message data: ${message.data}");

    if (message.notification != null) {
      print('Message also contained a notification: ${message.notification}');
      String payloadData = jsonEncode(message.data);

      if (kIsWeb) {
        // For web, we can show a simple dialog as a notification.
        showNotification(
          title: message.notification!.title!,
          body: message.notification!.body!,
        );
      } else {
        // For mobile, use the custom local notification service.
        PushNotifications.showSimpleNotification(
          title: message.notification!.title!,
          body: message.notification!.body!,
          payload: payloadData,
        );
      }
    }
  });

  // Handles when a user taps a notification and opens the app from the background state.
  FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
    print('A new onMessageOpenedApp event was published!');
    if (message.notification != null) {
      // Navigate to a specific screen based on the notification.
      navigatorKey.currentState!.pushNamed("/message", arguments: message);
    }
  });

  // Handles when the app is opened from a terminated state by tapping a notification.
  FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
    if (message != null) {
      print("Launched from terminated state by notification tap");
      // Delay navigation slightly to ensure the app has time to build the first screen.
      Future.delayed(const Duration(seconds: 1), () {
        navigatorKey.currentState!.pushNamed("/message", arguments: message);
      });
    }
  });
}

// Helper function to show a notification dialog on web.
void showNotification({required String title, required String body}) {
  showDialog(
    context: navigatorKey.currentContext!,
    builder: (context) => AlertDialog(
      title: Text(title),
      content: Text(body),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text("Ok"),
        ),
      ],
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: navigatorKey,
      title: 'Zoomfresh Admin',
      theme: ThemeData(
        fontFamily: 'Neuton',
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.red,
          background: Colors.white,
        ),
        useMaterial3: true,
      ),
      home: SplashScreen(),
    );
  }
}
