import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:user/product.dart';
import 'package:user/productdetail.dart';
import 'package:user/variant.dart';

class ProductListScreen extends StatefulWidget {
  final int categoryId;

  ProductListScreen({required this.categoryId});

  @override
  _ProductListScreenState createState() => _ProductListScreenState();
}

class _ProductListScreenState extends State<ProductListScreen> {
  List<Product>? allProducts;
  bool isLoading = true;
  bool isError = false;
  bool isFetching = false; // Prevent multiple API calls
  String searchQuery = "";
  @override
  void initState() {
    super.initState();
    fetchProductsByCategory();
  }

  Future<void> fetchProductsByCategory() async {
    // Prevent API calls if already fetching or data is loaded
    if (isFetching || allProducts != null) return;

    setState(() {
      isFetching = true; // Mark as fetching
    });

    try {
      final response = await http.post(Uri.parse('https://price.zoomfresh.co.in/api/productlist?category_id=${widget.categoryId}'));

      if (response.statusCode == 200) {
        final List<dynamic> responseData = json.decode(response.body)['data'];
        setState(() {
          allProducts = responseData.map((item) => Product.fromJson(item)).toList();
          isLoading = false;
        });
      } else {
        throw Exception('Failed to load data');
      }
    } catch (e) {
      setState(() {
        isError = true;
        isLoading = false;
      });
      print(e);
    } finally {
      setState(() {
        isFetching = false; // Reset fetching state
      });
    }
  }

  Future<void> _refreshData() async {
    setState(() {
      isLoading = true;
      isError = false;
      allProducts = null; // Clear existing data to refetch
    });
    await fetchProductsByCategory();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        title: Text(
          'Products List',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.red,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(70), // Adjust the height for the search box
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(25), // Circular edges for the search box
                        ),
                        child: TextField(
                          decoration: InputDecoration(
                            contentPadding: EdgeInsets.symmetric(vertical: 8),
                            hintText: 'Search products...',
                            filled: true,
                            fillColor: Colors.grey[200],
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide.none,
                            ),
                            prefixIcon: Icon(Icons.search, color: Colors.grey),
                          ),
                          onChanged: (query) {
                            setState(() {
                              searchQuery = query.toLowerCase(); // Update the search query
                            });
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 10),
            ],
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData, // Pull-to-refresh functionality
        child: isLoading
            ? Center(
                child: CircularProgressIndicator(
                  color: Colors.red,
                  strokeWidth: 2,
                ),
              )
            : isError
                ? Center(
                    child: Text('Failed to load data. Please try again later.'),
                  )
                : (allProducts == null || allProducts!.isEmpty)
                    ? Center(
                        child: Text('No products available.'),
                      )
                    : ListView.builder(
                        itemCount: _getFilteredProducts().length,
                        itemBuilder: (context, index) {
                          final product = _getFilteredProducts()[index];
                          return AnimatedOpacity(
                            opacity: product.isAvailable ? 1.0 : 0.5,
                            duration: Duration(milliseconds: 500),
                            child: GestureDetector(
                              onTap: () async {
                                await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => ProductDetailScreen(
                                      product: product,
                                      categoryId: product.category_id,
                                      onRefreshProductList: _refreshData,
                                    ),
                                  ),
                                );
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.0),
                                  border: Border.all(color: Colors.red),
                                ),
                                margin: EdgeInsets.all(8.0),
                                child: ListTile(
                                  leading: CachedNetworkImage(
                                    imageUrl: 'https://price.zoomfresh.co.in/storage/' + product.image,
                                    placeholder: (context, url) => CircularProgressIndicator(
                                      color: Colors.red,
                                      strokeWidth: 1,
                                    ),
                                    errorWidget: (context, url, error) => Icon(Icons.error),
                                  ),
                                  title: Text(product.name),
                                  subtitle: _buildPriceText(product.variants[0]), // assuming the first variant here
                                ),
                              ),
                            ),
                          );
                        },
                      ),
      ),
    );
  }

// Function to filter products based on the search query
  List<Product> _getFilteredProducts() {
    if (searchQuery.isEmpty) {
      return allProducts ?? [];
    }
    return allProducts!.where((product) => product.name.toLowerCase().contains(searchQuery.toLowerCase())).toList();
  }

// Function to refresh data

  Widget _buildPriceText(Variant variant) {
    if (variant.discountedPrice != 0) {
      return Row(
        children: [
          Text(
            '₹${variant.price.toString()}',
            style: TextStyle(
              decoration: TextDecoration.lineThrough,
              color: Colors.grey,
            ),
          ),
          Text(
            '  ₹${variant.discountedPrice.toString()}',
            style: TextStyle(color: Colors.green),
          ),
        ],
      );
    } else {
      return Text(
        '₹${variant.price.toString()}',
        style: TextStyle(color: Colors.green),
      );
    }
  }

  // Callback method to refresh product list
}
