import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:user/home.dart';

class LoginScreen extends StatefulWidget {
  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  bool isLoading = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _getDeviceToken();
  }

  void showLoginMessage(
      String message, Color color, BuildContext scaffoldContext) {
    ScaffoldMessenger.of(scaffoldContext).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
      ),
    );
  }

  Future<void> _getDeviceToken() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;
    try {
      String? token = await messaging.getToken();
      if (token != null) {
        print("Device Token: $token");
        // Save the token in a variable for later use
      } else {
        print("Failed to retrieve token.");
      }
    } catch (e) {
      print("Error retrieving device token: $e");
    }
  }
// In _LoginScreenState class

  // No changes needed for initState, _getDeviceToken, or showLoginMessage

  Future<void> loginUser(BuildContext context) async {
    // Changed parameter name for clarity
    // Check for mounted widget before using context
    if (!context.mounted) return;

    if (emailController.text.isEmpty || passwordController.text.isEmpty) {
      showLoginMessage('Please enter both email/username and password.',
          Colors.red, context);
      return;
    }

    setState(() {
      isLoading = true;
    });

    String? deviceToken = await FirebaseMessaging.instance.getToken();
    Map<String, String> loginData = {
      'email': emailController.text,
      'password': passwordController.text,
      'fcm_token': deviceToken ?? '',
    };

    try {
      final response = await http.post(
        Uri.parse('https://price.zoomfresh.co.in/api/login'),
        body: loginData,
      );

      // Important: Check if the widget is still mounted after the async call
      if (!context.mounted) return;

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 && responseData['status'] == 1) {
        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setBool('isLoggedIn', true);
        await prefs.setString(
            'userId', responseData['data']['user']['id'].toString());

        showLoginMessage('Login successful!', Colors.green, context);

        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => Home()),
        );
      } else {
        showLoginMessage(
            responseData['message'] ?? 'Login failed.', Colors.red, context);
      }
    } catch (e) {
      if (!context.mounted) return;
      showLoginMessage(
          'An error occurred. Please try again later.', Colors.red, context);
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor:
          Theme.of(context).colorScheme.surface, // M3 background color
      body: SingleChildScrollView(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minHeight: screenHeight,
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(
                horizontal: 24.0), // Consistent padding
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment:
                  CrossAxisAlignment.stretch, // Makes children fill width
              children: [
                Image.asset(
                  'images/logo3.png',
                  width: 150.0,
                  height: 150.0,
                ),
                const SizedBox(height: 48.0),

                // M3 TextField for Email
                TextField(
                  controller: emailController,
                  keyboardType: TextInputType.emailAddress,
                  decoration: const InputDecoration(
                    labelText: 'Email or Username',
                    prefixIcon: Icon(Icons.person_outline), // M3 uses icons
                    border: OutlineInputBorder(), // Standard M3 border
                  ),
                  style: const TextStyle(fontFamily: 'Neutraface'),
                ),
                const SizedBox(height: 20.0),

                // M3 TextField for Password
                TextField(
                  controller: passwordController,
                  obscureText: true,
                  decoration: const InputDecoration(
                    labelText: 'Password',
                    prefixIcon: Icon(Icons.lock_outline),
                    border: OutlineInputBorder(),
                  ),
                  style: const TextStyle(fontFamily: 'Neutraface'),
                ),
                const SizedBox(height: 48.0),

                // M3 Button with Loading Indicator
                // The button is disabled automatically when isLoading is true
                // by setting onPressed to null.
                SizedBox(
                  height: 50,
                  child: ElevatedButton(
                    onPressed: isLoading
                        ? null
                        : () {
                            // No need for Builder or passing context anymore
                            loginUser(context);
                          },
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                      // Use M3 colors from the theme
                      backgroundColor: Colors.red,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                    child: isLoading
                        ? Container(
                            width: 24,
                            height: 24,
                            padding: const EdgeInsets.all(2.0),
                            child: CircularProgressIndicator(
                              color: Theme.of(context).colorScheme.onPrimary,
                              strokeWidth: 3,
                            ),
                          )
                        : Text(
                            'LOGIN',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Neutraface',
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
