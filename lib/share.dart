import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';

void shareOrderDetails(Map<String, dynamic> order, List<dynamic> orderItems) {

String extractAndAddTime(String status) {
  // Extract the part inside the quotes using a RegExp
  final regex = RegExp(r'\[\[2,"(.*?)"\]\]');
  final match = regex.firstMatch(status);

  // If a match is found, proceed with adding the time
  if (match != null) {
    // Extract the date-time string
    String dateTimeString = match.group(1)!;

    try {
      // Parse the date-time string into a DateTime object
      DateFormat dateFormat = DateFormat("dd-MM-yyyy hh:mm:ssa");
      DateTime parsedDate = dateFormat.parse(dateTimeString.toUpperCase()); // Convert AM/PM to uppercase

      // Add 5 hours and 30 minutes
      DateTime updatedDate = parsedDate.add(Duration(hours: 5, minutes: 30));

      // Return the updated date-time in the same format
      return dateFormat.format(updatedDate);
    } catch (e) {
      return "Error parsing date: $e";
    }
  } else {
    return "Invalid status";
  }
}
  String orderDetails = '''
Order ID: ${order['order_id']}
Name: ${order['user_name']}
Address: ${order['address']}
Mobile: ${order['mobile']}
Payment Method: ${order['payment_method']}
Ordered At: ${extractAndAddTime(order['status'])}
Slot : ${(order['delivery_time'])}
Total: ₹${order['final_total']}

Products:
${orderItems.map((item) {
    return '''
- Product Name: ${item['product_name'] ?? 'N/A'}
  Variant: ${item['variant_name'] ?? 'N/A'}
  Cut Method: ${item['cut_method'] ?? 'N/A'}
  Quantity: ${item['quantity'] ?? 'N/A'}
  Price: ₹${item['sub_total'] ?? 'N/A'}
''';
  }).join('\n')}
''';

  Share.share(orderDetails, subject: 'Order Details');
}
