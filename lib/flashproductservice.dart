import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:user/flashproductsmodel.dart';

Future<List<FlashSaleProduct>> fetchFlashSaleProducts() async {
  final response = await http
      .get(Uri.parse('https://price.zoomfresh.co.in/api/flashsaleproductlist'));

  if (response.statusCode == 200) {
    final data = json.decode(response.body);
    if (data['status'] == 'success') {
      final List<dynamic> productsJson = data['data'];
      return productsJson
          .map((json) => FlashSaleProduct.fromJson(json))
          .toList();
    } else {
      throw Exception('Failed to load products');
    }
  } else {
    throw Exception('Failed to load products');
  }
}
