class Variant {
  final int id;
  final double measurement;
  final double price;
  final double discountedPrice;

  Variant({
    required this.id,
    required this.measurement,
    required this.price,
    required this.discountedPrice
  });

  factory Variant.fromJson(Map<String, dynamic> json) {
    return Variant(
      id: json['id'],
      measurement: json['measurement'].toDouble(),
      price: json['price'].toDouble(),
      discountedPrice: json['discounted_price'].toDouble(),
    );
  }
}

