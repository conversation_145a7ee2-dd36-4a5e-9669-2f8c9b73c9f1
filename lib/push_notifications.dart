import 'dart:convert';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:user/main.dart';
import 'package:user/orders.dart';

class PushNotifications {
  static final _firebaseMessaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // Request notification permission
  static Future<void> init() async {
    // Request permission for notifications
    await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    // Get the FCM token for this device
    await getFCMToken();

    // Initialize local notifications
    await localNotiInit();
  }

  // Get the FCM device token
  static Future<String?> getFCMToken({int maxRetries = 3}) async {
    try {
      String? token;
      if (kIsWeb) {
        token = await _firebaseMessaging.getToken(
          vapidKey:
              "BJzRufH-VxRc7wLunA6WOaf-gVurFKhDluPRFB8644PQHw6OfWH8uzybtYsFBTA326_yy3PEG-L7OK_ojVsMmrI",
        );
        print("Web device token: $token");
      } else {
        token = await _firebaseMessaging.getToken();
        print("Android/iOS device token: $token");
      }
      return token;
    } catch (e) {
      print("Failed to get device token: $e");
      if (maxRetries > 0) {
        await Future.delayed(Duration(seconds: 10));
        return getFCMToken(maxRetries: maxRetries - 1);
      }
      return null;
    }
  }

  // Initialize local notifications
  static Future<void> localNotiInit() async {
    // Android-specific notification initialization settings
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // iOS-specific notification initialization settings
    final DarwinInitializationSettings initializationSettingsDarwin =
        DarwinInitializationSettings();

    // Combined initialization settings for both platforms
    final InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsDarwin,
    );

    // Initialize the local notifications plugin
    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: onNotificationTap,
      onDidReceiveBackgroundNotificationResponse: onNotificationTap,
    );
  }

  // Handle notification tap (when user taps on a notification)
  static void onNotificationTap(NotificationResponse notificationResponse) {
    // Navigate to the orders page when a notification is tapped
    navigatorKey.currentState!.push(
      MaterialPageRoute(builder: (context) => MyOrders()),
    );
  }

  // Show a simple notification with custom sound
  static Future<void> showSimpleNotification({
    required String title,
    required String body,
    required String payload,
  }) async {
    // Android-specific notification details with custom sound
    const AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
      'your_channel_id', // Channel ID
      'your_channel_name', // Channel name
      channelDescription: 'your_channel_description', // Channel description
      importance: Importance.max, // Set importance
      priority: Priority.high, // Set priority
      ticker: 'ticker', // Set ticker text
      sound: RawResourceAndroidNotificationSound('custom_sound'), // Custom sound
    );

    // iOS-specific notification details with custom sound
    const DarwinNotificationDetails iosNotificationDetails =
        DarwinNotificationDetails(
      sound: 'custom_sound.wav', // Custom sound for iOS
    );

    // Combined notification details for both platforms
    const NotificationDetails notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
      iOS: iosNotificationDetails,
    );

    // Show the notification
    await _flutterLocalNotificationsPlugin.show(
      0, // Notification ID
      title, // Title of the notification
      body, // Body of the notification
      notificationDetails, // Notification details (Android + iOS)
      payload: payload, // Payload for extra data
    );
  }

  // Listen for background notifications
  static Future<void> _firebaseBackgroundMessage(RemoteMessage message) async {
    if (message.notification != null) {
      print("Notification received in background");
      showSimpleNotification(
        title: message.notification!.title!,
        body: message.notification!.body!,
        payload: jsonEncode(message.data),
      );
    }
  }

  // Initialize Firebase Messaging
  static Future<void> initializeFirebase() async {
    // Initialize Firebase
    await Firebase.initializeApp();
    
    // Handle notification taps when the app is in the background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print("Notification tapped in background");
      navigatorKey.currentState!.pushNamed("/message", arguments: message);
    });

    // Listen to foreground notifications
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print("Notification received in foreground");
      showSimpleNotification(
        title: message.notification!.title!,
        body: message.notification!.body!,
        payload: jsonEncode(message.data),
      );
    });

    // Listen for notifications when the app is terminated
    final RemoteMessage? message =
        await FirebaseMessaging.instance.getInitialMessage();
    if (message != null) {
      print("App launched from terminated state");
      navigatorKey.currentState!.pushNamed("/message", arguments: message);
    }

    // Listen to background notifications
    FirebaseMessaging.onBackgroundMessage(_firebaseBackgroundMessage);
  }
}
