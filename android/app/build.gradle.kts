import java.util.Properties

// In android/app/build.gradle.kts

plugins {
    id("com.android.application")
    // START: FlutterFire Configuration
    id("com.google.gms.google-services")
    // END: FlutterFire Configuration
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
}

// This function reads properties from local.properties
fun getLocalProperty(key: String, project: org.gradle.api.Project): String {
    val properties = Properties()
    val localPropertiesFile = project.rootProject.file("local.properties")
    if (localPropertiesFile.exists()) {
        localPropertiesFile.inputStream().use { properties.load(it) }
    }
    return properties.getProperty(key) ?: ""
}

// Read version information from Flutter
val flutterVersionCode: Int = getLocalProperty("flutter.versionCode", project).toIntOrNull() ?: 1
val flutterVersionName: String = getLocalProperty("flutter.versionName", project)

android {
    namespace = "com.example.user" // IMPORTANT: Replace with your actual package name if different
    compileSdk = 34 // We will hardcode this to ensure it's correct

    // FIX #1: Set the NDK version as required by your plugins.
    ndkVersion = "27.0.12077973"

    compileOptions {
        // FIX #2, PART 1: Enable Core Library Desugaring. This is the correct location.
        isCoreLibraryDesugaringEnabled = true
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }

    sourceSets {
        getByName("main") {
            java.srcDir("src/main/kotlin")
        }
    }

    defaultConfig {
        applicationId = "com.example.user" // IMPORTANT: Replace with your actual package name if different
        minSdk = 21
        targetSdk = 34
        versionCode = flutterVersionCode
        versionName = flutterVersionName
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("debug")
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // FIX #2, PART 2: Add the Desugaring library dependency.
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
}